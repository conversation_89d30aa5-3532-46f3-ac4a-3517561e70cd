"""
Enhanced Central API Manager for Recipe Finder.

This module provides a comprehensive API management system with the following key features:

1. **Universal Fallback System**:
   - Spoonacular API as primary source with automatic Tasty API fallback
   - Tasty API daily limits removed - now serves as unlimited fallback
   - TheMealDB as final free fallback option

2. **Enhanced FoodData Central Integration**:
   - Comprehensive nutritional data retrieval and processing
   - Advanced nutrient standardization and analysis
   - Robust error handling and retry logic
   - Detailed nutritional filtering capabilities

3. **Intelligent API Selection**:
   - Automatic parameter mapping between APIs
   - Context-aware fallback strategies
   - Health monitoring and status checking

4. **Advanced Caching**:
   - Redis-based caching with fallback mechanisms
   - Intelligent cache key generation
   - Extended cache durations for popular recipes

5. **Nutritional Analysis**:
   - Comprehensive macronutrient and micronutrient analysis
   - Dietary flag detection (high protein, low fat, etc.)
   - Recipe filtering based on nutritional criteria
   - Health indicator calculations

Usage Examples:
    # Basic search with automatic fallback
    recipes = search_recipes("chicken pasta")

    # Comprehensive search with nutrition
    results = comprehensive_recipe_search(
        "healthy salad",
        include_nutrition=True,
        nutrition_filters={"max_calories": 400, "min_protein": 15}
    )

    # Get detailed nutrition info
    nutrition = get_nutrition_info(["chicken breast", "broccoli"], detailed=True)

    # Check API health
    health = get_api_health_status()
"""

from typing import List, Dict, Any, Optional
import logging
import os
import json
import hashlib
from dotenv import load_dotenv
from django.core.cache import cache
from .cache_utils import (
    safe_cache_get,
    safe_cache_set,
    safe_cache_delete,
    RECIPE_CACHE_DURATION,
    API_RATE_LIMIT_CACHE_DURATION
)

# Load environment variables from .env file
load_dotenv()

SPOONACULAR_API_KEY = os.getenv("SPOONACULAR_API_KEY")
TASTY_API_HOST = os.getenv("TASTY_API_HOST")
TASTY_API_KEY = os.getenv("TASTY_API_KEY")
FOODDATA_API_KEY = os.getenv("FOODDATA_API_KEY")

# TheMealDB API (free, no key required)
THEMEALDB_BASE_URL = "https://www.themealdb.com/api/json/v1/1"

# Tasty API configuration (no daily limits - removed restrictions)
# Tasty API now serves as universal fallback for all Spoonacular queries

# Cache duration constants are now imported from cache_utils

def _generate_cache_key(prefix: str, query: str, filters: Optional[Dict[str, Any]] = None) -> str:
    """
    Generate a consistent cache key with sorted JSON serialization for filters.

    Args:
        prefix: Cache key prefix (e.g., 'search_recipes', 'recipe_details')
        query: Main query parameter
        filters: Optional filters dictionary

    Returns:
        Consistent cache key string
    """
    if filters:
        # Sort keys to ensure consistent serialization
        filters_key = json.dumps(filters, sort_keys=True)
    else:
        filters_key = "none"

    return f"{prefix}:{query}:{filters_key}"

# Rate limiting configuration
RATE_LIMIT_DELAYS = {
    'spoonacular': 0.1,  # 100ms between requests
    'tasty': 0.2,        # 200ms between requests
    'fooddata': 0.15,    # 150ms between requests
    'themealdb': 0.05,   # 50ms between requests (free API)
}

# Retry configuration
MAX_RETRIES = 3
BASE_RETRY_DELAY = 1  # seconds

# Debug: Log API config (remove this in production)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

logger.debug(f"TASTY_API_HOST: {'Set' if TASTY_API_HOST else 'Not set'}")
logger.debug(f"TASTY_API_KEY: {'Set' if TASTY_API_KEY else 'Not set'}")
logger.debug(f"SPOONACULAR_API_KEY: {'Set' if SPOONACULAR_API_KEY else 'Not set'}")


# --- Main API Manager Functions ---
import requests
import time
import random
from requests.exceptions import HTTPError

def _make_api_request(url: str, api_name: str, headers: Optional[Dict[str, str]] = None,
                     params: Optional[Dict[str, Any]] = None, timeout: int = 10) -> requests.Response:
    """
    Make an API request with rate limiting, retry logic, and 429 handling.

    Args:
        url: The API endpoint URL
        api_name: Name of the API for rate limiting ('spoonacular', 'tasty', 'fooddata', 'themealdb')
        headers: Optional request headers
        params: Optional request parameters
        timeout: Request timeout in seconds

    Returns:
        requests.Response object

    Raises:
        HTTPError: If all retries fail
    """
    # Apply rate limiting delay
    if api_name in RATE_LIMIT_DELAYS:
        time.sleep(RATE_LIMIT_DELAYS[api_name])

    for attempt in range(MAX_RETRIES + 1):
        try:
            logger.debug(f"Making {api_name} API request (attempt {attempt + 1}): {url}")
            response = requests.get(url, headers=headers, params=params, timeout=timeout)

            # Handle rate limiting (429 status code)
            if response.status_code == 429:
                if attempt < MAX_RETRIES:
                    # Exponential backoff with jitter
                    delay = BASE_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"{api_name} API rate limited (429). Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"{api_name} API rate limited after {MAX_RETRIES} retries")
                    response.raise_for_status()

            # Handle other HTTP errors
            response.raise_for_status()
            return response

        except HTTPError as e:
            if attempt < MAX_RETRIES and e.response.status_code in [429, 500, 502, 503, 504]:
                # Retry on rate limiting and server errors
                delay = BASE_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"{api_name} API error {e.response.status_code}. Retrying in {delay:.2f} seconds...")
                time.sleep(delay)
                continue
            else:
                logger.error(f"{api_name} API request failed after {attempt + 1} attempts: {e}")
                raise
        except Exception as e:
            if attempt < MAX_RETRIES:
                delay = BASE_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"{api_name} API request failed. Retrying in {delay:.2f} seconds... Error: {e}")
                time.sleep(delay)
                continue
            else:
                logger.error(f"{api_name} API request failed after {MAX_RETRIES + 1} attempts: {e}")
                raise




def search_recipes(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes using Spoonacular API by default with Tasty API as universal fallback.
    Falls back to TheMealDB if both fail.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_recipes", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try Spoonacular API first
    try:
        results = _spoonacular_search_recipes(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logging.error(f"Spoonacular API search failed: {e}")
        # Check if it's a payment error (402) and log appropriately
        if hasattr(e, 'response') and e.response.status_code == 402:
            logging.warning("Spoonacular API quota exceeded (402). Falling back to Tasty API.")

    # Universal fallback to Tasty API
    try:
        results = _spoonacular_to_tasty_fallback(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logging.error(f"Tasty API fallback failed: {e}")

    # Final fallback to TheMealDB (free API)
    try:
        results = _themealdb_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logging.error(f"TheMealDB API search failed: {e}")

    return []

def get_recipe_details(recipe_id: str, source: str = 'tasty') -> Dict[str, Any]:
    """
    Fetch recipe details (ingredients, instructions, image, etc.) from the given source.
    Falls back to other sources if the primary source fails.
    Returns standardized recipe details with robust Redis caching.
    """
    cache_key = f"get_recipe_details:{recipe_id}:{source}"
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        if source == 'tasty':
            details = _tasty_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
        elif source == 'themealdb':
            details = _themealdb_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
        elif source == 'spoonacular':
            details = _spoonacular_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
    except Exception as e:
        logger.error(f"{source.title()} API get details failed: {e}")

    # Fallback to other sources if primary source fails
    if source != 'spoonacular':
        try:
            details = _spoonacular_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
        except Exception as e:
            logger.error(f"Spoonacular API fallback failed: {e}")

    return {}

# --- Universal Fallback System ---
def _map_spoonacular_to_tasty_params(spoonacular_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Map Spoonacular API parameters to equivalent Tasty API parameters.

    Args:
        spoonacular_params: Dictionary of Spoonacular API parameters

    Returns:
        Dictionary of equivalent Tasty API parameters
    """
    tasty_params = {}

    # Map basic query parameter
    if 'query' in spoonacular_params:
        tasty_params['q'] = spoonacular_params['query']

    # Map result count (Tasty uses 'size', Spoonacular uses 'number')
    if 'number' in spoonacular_params:
        tasty_params['size'] = min(spoonacular_params['number'], 40)  # Tasty max is 40
    else:
        tasty_params['size'] = 20  # Default

    # Set default pagination
    tasty_params['from'] = 0

    # Note: Tasty API has limited filtering compared to Spoonacular
    # Complex filters like cuisine, diet, etc. will be handled via post-processing

    return tasty_params

def _spoonacular_to_tasty_fallback(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Universal fallback function that calls Tasty API when Spoonacular fails.
    Maps Spoonacular-style parameters to Tasty API format.

    Args:
        query: Search query string
        filters: Optional filters dictionary (Spoonacular format)

    Returns:
        List of normalized recipe dictionaries from Tasty API
    """
    try:
        logger.info(f"Using Tasty API as fallback for query: {query}")

        # Use the existing Tasty search function with filter mapping
        return _tasty_search_recipes(query, filters)

    except Exception as e:
        logger.error(f"Tasty API fallback failed: {e}")
        return []

# --- Tasty API Helper Functions (No Daily Limits) ---
# Daily limits removed - Tasty API now serves as universal fallback

# --- Internal Helper Functions for Tasty API ---
def _tasty_search_recipes(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Calls the Tasty API to search for recipes by query and optional filters.
    For African cuisine, post-filters the results to only include African recipes.
    Returns a list of normalized recipe dicts.
    """
    try:
        if not TASTY_API_HOST or not TASTY_API_KEY:
            logger.error("Tasty API credentials not configured")
            return []

        # Tasty API daily limits removed - now available as universal fallback

        url = f"https://{TASTY_API_HOST}/recipes/list"
        headers = {
            "x-rapidapi-host": TASTY_API_HOST,
            "x-rapidapi-key": TASTY_API_KEY,
        }
        params = {
            "q": query,
            "from": 0,
            "size": 40,  # Increased size since we'll filter some out
        }

        # Apply additional filters from quick filters
        if filters:
            # Add tag keywords to the query for better matching
            if filters.get('tags'):
                tag_keywords = ' '.join(filters['tags'])
                params['q'] = f"{query} {tag_keywords}"
            
        logger.debug(f"Calling Tasty API: {url} with params: {params}")
        response = _make_api_request(url, 'tasty', headers=headers, params=params, timeout=10)

        # Daily request counting removed - Tasty API now unlimited within service limits

        data = response.json()
        
        logger.debug(f"Tasty API response status: {response.status_code}")
        logger.debug(f"Tasty API response data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        
        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Tasty API")

        # Filter for African cuisine if specified
        if filters and filters.get('cuisine') == 'african':
            african_keywords = ['african', 'ethiopian', 'nigerian', 'moroccan', 'egyptian', 'kenyan', 
                              'south african', 'west african', 'east african', 'north african', 
                              'central african', 'maghreb', 'suya', 'jollof', 'fufu', 'injera']
            
            filtered_recipes = []
            for recipe in recipes:
                # Check recipe tags
                tags = [t.get('display_name', '').lower() for t in recipe.get('tags', [])]
                # Check recipe name and description
                name = recipe.get('name', '').lower()
                description = recipe.get('description', '').lower()
                
                # Check if any African keywords are present in tags, name, or description
                is_african = any(kw in ' '.join(tags + [name, description]) for kw in african_keywords)
                if is_african:
                    filtered_recipes.append(recipe)
            
            recipes = filtered_recipes[:20]  # Limit to 20 recipes after filtering
        else:
            # For global cuisine, just return first 20 recipes
            recipes = recipes[:20]
        
        return [_normalize_tasty_recipe(r) for r in recipes]
        
    except Exception as e:
        logger.error(f"Error in _tasty_search_recipes: {str(e)}", exc_info=True)
        raise

# --- Internal Helper Functions for Spoonacular API ---
def _spoonacular_search_recipes(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Calls the Spoonacular API to search for recipes by query and optional filters.
    Handles African vs Global cuisine filtering.
    Returns a list of normalized recipe dicts.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            # Immediately fallback to Tasty if no API key
            return _spoonacular_to_tasty_fallback(query, filters)

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "query": query,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Set cuisine filter
        if filters and filters.get('cuisine') == 'african':
            params['cuisine'] = 'african'
        else:
            # For global cuisine, exclude African to avoid duplication
            params['excludeCuisine'] = 'african'

        # Apply additional filters from quick filters
        if filters:
            # Time-based filters
            if filters.get('maxReadyTime'):
                params['maxReadyTime'] = filters['maxReadyTime']

            # Sorting preferences
            if filters.get('sort'):
                if filters['sort'] == 'popularity':
                    params['sort'] = 'popularity'
                elif filters['sort'] == 'price':
                    params['sort'] = 'price'

            # Exclude ingredients (for family-friendly recipes)
            if filters.get('excludeIngredients'):
                params['excludeIngredients'] = ','.join(filters['excludeIngredients'])

            # Tags are handled through query modification for Spoonacular
            if filters.get('tags'):
                # Add tag keywords to the query for better matching
                tag_keywords = ' '.join(filters['tags'])
                params['query'] = f"{query} {tag_keywords}"

        logger.debug(f"Calling Spoonacular API: {url} with params: {params}")
        response = _make_api_request(url, 'spoonacular', params=params, timeout=10)
        data = response.json()

        logger.debug(f"Spoonacular API response status: {response.status_code}")
        logger.debug(f"Spoonacular API response data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular API")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_recipes: {str(e)}", exc_info=True)
        # Universal fallback to Tasty API on any Spoonacular failure
        logger.info("Attempting Tasty API fallback for Spoonacular search failure")
        try:
            return _spoonacular_to_tasty_fallback(query, filters)
        except Exception as fallback_error:
            logger.error(f"Tasty API fallback also failed: {fallback_error}")
            raise e  # Re-raise original Spoonacular error

def _spoonacular_get_recipe_details(recipe_id: str) -> Dict[str, Any]:
    """
    Calls the Spoonacular API to get full details for a single recipe by ID.
    Returns a normalized recipe dict.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return {}
            
        url = f"https://api.spoonacular.com/recipes/{recipe_id}/information"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "includeNutrition": True,
        }
        logger.debug(f"Calling Spoonacular API: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        logger.debug(f"Spoonacular API response status: {response.status_code}")
        logger.debug(f"Spoonacular API response data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        
        return _normalize_spoonacular_recipe(data)
        
    except Exception as e:
        logger.error(f"Error in _spoonacular_get_recipe_details: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by name using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "query": query,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']
            if filters.get('diet'):
                params['diet'] = filters['diet']
            if filters.get('intolerances'):
                params['intolerances'] = filters['intolerances']
            if filters.get('maxReadyTime'):
                params['maxReadyTime'] = filters['maxReadyTime']
            if filters.get('number'):
                params['number'] = filters['number']
            if filters.get('sort'):
                params['sort'] = filters['sort']

        logger.debug(f"Calling Spoonacular API for name search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular name search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_name: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_cuisine(cuisine: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by cuisine using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "cuisine": cuisine,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('query'):
                params['query'] = filters['query']
            if filters.get('diet'):
                params['diet'] = filters['diet']
            if filters.get('intolerances'):
                params['intolerances'] = filters['intolerances']
            if filters.get('maxReadyTime'):
                params['maxReadyTime'] = filters['maxReadyTime']

        logger.debug(f"Calling Spoonacular API for cuisine search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular cuisine search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_cuisine: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_diet(diet_restrictions: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by dietary restrictions using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Parse diet restrictions - could be diet type or intolerances
        # Common diets: vegetarian, vegan, pescetarian, paleo, ketogenic, etc.
        # Common intolerances: gluten, dairy, egg, soy, etc.
        diet_keywords = ['vegetarian', 'vegan', 'pescetarian', 'paleo', 'ketogenic', 'whole30', 'primal']
        intolerance_keywords = ['gluten', 'dairy', 'egg', 'soy', 'wheat', 'peanut', 'tree nut', 'shellfish', 'sesame', 'sulfite']

        diet_restrictions_lower = diet_restrictions.lower()

        # Check if it's a diet type
        for diet in diet_keywords:
            if diet in diet_restrictions_lower:
                params['diet'] = diet
                break

        # Check if it's an intolerance
        found_intolerances = []
        for intolerance in intolerance_keywords:
            if intolerance in diet_restrictions_lower:
                found_intolerances.append(intolerance)

        if found_intolerances:
            params['intolerances'] = ','.join(found_intolerances)

        # If no specific diet or intolerance found, use as general query
        if 'diet' not in params and 'intolerances' not in params:
            params['query'] = diet_restrictions

        # Apply additional filters if provided
        if filters:
            if filters.get('query'):
                params['query'] = filters['query']
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']
            if filters.get('maxReadyTime'):
                params['maxReadyTime'] = filters['maxReadyTime']

        logger.debug(f"Calling Spoonacular API for diet search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular diet search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_diet: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_time(max_time: int, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by cooking time using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "maxReadyTime": max_time,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('query'):
                params['query'] = filters['query']
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']
            if filters.get('diet'):
                params['diet'] = filters['diet']
            if filters.get('intolerances'):
                params['intolerances'] = filters['intolerances']

        logger.debug(f"Calling Spoonacular API for time search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular time search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_time: {str(e)}", exc_info=True)
        raise

# --- Internal Helper Functions for TheMealDB API ---
def _themealdb_search_by_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by name using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/search.php"
        params = {"s": query}

        logger.debug(f"Calling TheMealDB API for name search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []  # Handle null response
        logger.debug(f"Found {len(meals)} meals from TheMealDB name search")

        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_name: {str(e)}", exc_info=True)
        raise

def _themealdb_search_by_cuisine(area: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by area/cuisine using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/filter.php"
        params = {"a": area}

        logger.debug(f"Calling TheMealDB API for area search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []
        logger.debug(f"Found {len(meals)} meals from TheMealDB area search")

        # Note: Filter endpoint returns limited data, so we need to fetch full details
        # For now, return the basic data and enhance later if needed
        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_cuisine: {str(e)}", exc_info=True)
        raise

def _themealdb_search_by_category(category: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by category using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/filter.php"
        params = {"c": category}

        logger.debug(f"Calling TheMealDB API for category search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []
        logger.debug(f"Found {len(meals)} meals from TheMealDB category search")

        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_category: {str(e)}", exc_info=True)
        raise

def _themealdb_search_by_ingredient(ingredient: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by main ingredient using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/filter.php"
        params = {"i": ingredient}

        logger.debug(f"Calling TheMealDB API for ingredient search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []
        logger.debug(f"Found {len(meals)} meals from TheMealDB ingredient search")

        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_ingredient: {str(e)}", exc_info=True)
        raise

def _themealdb_get_random_meals(count: int = 1) -> List[Dict[str, Any]]:
    """
    Get random meals from TheMealDB API.
    """
    try:
        meals = []
        for _ in range(min(count, 10)):  # Limit to 10 to avoid too many requests
            url = f"{THEMEALDB_BASE_URL}/random.php"

            logger.debug(f"Calling TheMealDB API for random meal: {url}")
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()

            meal_data = data.get("meals", [])
            if meal_data:
                meals.extend(meal_data)

        logger.debug(f"Found {len(meals)} random meals from TheMealDB")
        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_get_random_meals: {str(e)}", exc_info=True)
        raise

def _themealdb_get_recipe_details(recipe_id: str) -> Dict[str, Any]:
    """
    Get recipe details by ID from TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/lookup.php"
        params = {"i": recipe_id}

        logger.debug(f"Calling TheMealDB API for recipe details: {url} with params: {params}")
        response = _make_api_request(url, 'themealdb', params=params, timeout=10)
        data = response.json()

        meals = data.get("meals", [])
        if meals:
            logger.debug(f"Found recipe details for ID {recipe_id} from TheMealDB")
            return _normalize_themealdb_recipe(meals[0])
        else:
            logger.warning(f"No recipe found for ID {recipe_id} in TheMealDB")
            return {}

    except Exception as e:
        logger.error(f"Error in _themealdb_get_recipe_details: {str(e)}", exc_info=True)
        raise

# --- Enhanced FoodData Central API Implementation ---
def _fooddata_search_food(ingredient: str, page_size: int = 5) -> List[Dict[str, Any]]:
    """
    Search for food items in FoodData Central API with enhanced error handling.

    Args:
        ingredient: Ingredient name to search for
        page_size: Number of results to return (max 200)

    Returns:
        List of food items with nutritional data
    """
    try:
        if not FOODDATA_API_KEY:
            logger.warning("FoodData Central API key not configured")
            return []

        url = "https://api.nal.usda.gov/fdc/v1/foods/search"
        params = {
            "api_key": FOODDATA_API_KEY,
            "query": ingredient,
            "pageSize": min(page_size, 200),  # API limit is 200
            "dataType": ["Foundation", "SR Legacy"],  # Focus on high-quality data
            "sortBy": "dataType.keyword",
            "sortOrder": "asc"
        }

        logger.debug(f"Calling FoodData Central API for ingredient: {ingredient}")
        response = _make_api_request(url, 'fooddata', params=params, timeout=15)
        data = response.json()

        foods = data.get("foods", [])
        logger.debug(f"Found {len(foods)} food items for ingredient: {ingredient}")

        return foods

    except Exception as e:
        logger.error(f"FoodData Central search failed for ingredient '{ingredient}': {e}")
        return []

def _fooddata_get_detailed_nutrition(fdc_id: str) -> Dict[str, Any]:
    """
    Get detailed nutritional information for a specific food item by FDC ID.

    Args:
        fdc_id: FoodData Central food ID

    Returns:
        Detailed nutritional information dictionary
    """
    try:
        if not FOODDATA_API_KEY:
            return {}

        url = f"https://api.nal.usda.gov/fdc/v1/food/{fdc_id}"
        params = {
            "api_key": FOODDATA_API_KEY,
            "nutrients": [  # Request specific important nutrients
                "203",  # Protein
                "204",  # Total lipid (fat)
                "205",  # Carbohydrate, by difference
                "208",  # Energy (calories)
                "269",  # Sugars, total
                "291",  # Fiber, total dietary
                "301",  # Calcium
                "303",  # Iron
                "307",  # Sodium
                "401",  # Vitamin C
                "404",  # Thiamin (Vitamin B1)
                "405",  # Riboflavin (Vitamin B2)
                "406",  # Niacin (Vitamin B3)
                "415",  # Vitamin B6
                "418",  # Vitamin B12
                "421",  # Choline
                "430"   # Vitamin K
            ]
        }

        logger.debug(f"Getting detailed nutrition for FDC ID: {fdc_id}")
        response = _make_api_request(url, 'fooddata', params=params, timeout=15)
        data = response.json()

        return data

    except Exception as e:
        logger.error(f"FoodData Central detailed nutrition failed for ID '{fdc_id}': {e}")
        return {}

def _fooddata_get_nutrition_info(ingredient_list: List[str]) -> Dict[str, Any]:
    """
    Enhanced FoodData Central API nutrition fetching with comprehensive data processing.
    Returns a normalized nutrition dict with detailed nutritional information.
    """
    if not ingredient_list:
        return {"nutrition": {}, "source": "fooddata", "details": []}

    nutrition_summary = {}
    detailed_foods = []
    processed_count = 0

    for ingredient in ingredient_list[:10]:  # Limit to 10 ingredients to avoid API overload
        try:
            # Clean ingredient name for better search results
            clean_ingredient = _clean_ingredient_name(ingredient)
            if not clean_ingredient:
                continue

            # Search for food items
            foods = _fooddata_search_food(clean_ingredient, page_size=3)
            if not foods:
                logger.debug(f"No FoodData results found for ingredient: {ingredient}")
                continue

            # Use the best match (first result is usually most relevant)
            best_food = foods[0]
            food_nutrients = best_food.get("foodNutrients", [])

            # Process nutritional data
            food_nutrition = {}
            for nutrient in food_nutrients:
                nutrient_name = nutrient.get("nutrientName", "")
                nutrient_number = nutrient.get("nutrientNumber", "")
                amount = nutrient.get("value")
                unit = nutrient.get("unitName", "")

                if nutrient_name and amount is not None and amount > 0:
                    # Standardize nutrient names
                    standardized_name = _standardize_nutrient_name(nutrient_name, nutrient_number)

                    if standardized_name not in nutrition_summary:
                        nutrition_summary[standardized_name] = {
                            "amount": 0,
                            "unit": unit,
                            "nutrient_number": nutrient_number
                        }

                    # Sum up nutrients across ingredients
                    nutrition_summary[standardized_name]["amount"] += amount
                    food_nutrition[standardized_name] = {"amount": amount, "unit": unit}

            # Store detailed food information
            detailed_foods.append({
                "ingredient": ingredient,
                "fdc_id": best_food.get("fdcId"),
                "description": best_food.get("description", ""),
                "data_type": best_food.get("dataType", ""),
                "nutrition": food_nutrition
            })

            processed_count += 1

        except Exception as e:
            logger.warning(f"Failed to process nutrition for ingredient '{ingredient}': {e}")
            continue

    logger.info(f"Successfully processed nutrition data for {processed_count}/{len(ingredient_list)} ingredients")

    return {
        "nutrition": nutrition_summary,
        "source": "fooddata",
        "details": detailed_foods,
        "processed_ingredients": processed_count,
        "total_ingredients": len(ingredient_list)
    }

def _clean_ingredient_name(ingredient: str) -> str:
    """
    Clean ingredient name for better FoodData Central API search results.

    Args:
        ingredient: Raw ingredient string

    Returns:
        Cleaned ingredient name
    """
    if not ingredient or not isinstance(ingredient, str):
        return ""

    # Remove common measurement words and quantities
    import re

    # Remove measurements and quantities
    cleaned = re.sub(r'\b\d+(?:\.\d+)?\s*(?:cups?|tbsp|tsp|oz|lbs?|pounds?|grams?|kg|ml|liters?)\b', '', ingredient, flags=re.IGNORECASE)

    # Remove common cooking terms
    cooking_terms = ['chopped', 'diced', 'sliced', 'minced', 'fresh', 'dried', 'ground', 'whole', 'large', 'small', 'medium']
    for term in cooking_terms:
        cleaned = re.sub(rf'\b{term}\b', '', cleaned, flags=re.IGNORECASE)

    # Remove extra whitespace and punctuation
    cleaned = re.sub(r'[,\(\)]', ' ', cleaned)
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()

    return cleaned

def _standardize_nutrient_name(nutrient_name: str, nutrient_number: str = "") -> str:
    """
    Standardize nutrient names for consistent aggregation.

    Args:
        nutrient_name: Original nutrient name from API
        nutrient_number: Nutrient number for precise identification

    Returns:
        Standardized nutrient name
    """
    # Map of nutrient numbers to standardized names
    nutrient_mapping = {
        "203": "Protein",
        "204": "Total Fat",
        "205": "Carbohydrates",
        "208": "Calories",
        "269": "Total Sugars",
        "291": "Dietary Fiber",
        "301": "Calcium",
        "303": "Iron",
        "307": "Sodium",
        "401": "Vitamin C",
        "404": "Thiamin (B1)",
        "405": "Riboflavin (B2)",
        "406": "Niacin (B3)",
        "415": "Vitamin B6",
        "418": "Vitamin B12",
        "421": "Choline",
        "430": "Vitamin K"
    }

    # Use nutrient number mapping if available
    if nutrient_number in nutrient_mapping:
        return nutrient_mapping[nutrient_number]

    # Fallback to name-based standardization
    name_lower = nutrient_name.lower()

    if "protein" in name_lower:
        return "Protein"
    elif "fat" in name_lower and "total" in name_lower:
        return "Total Fat"
    elif "carbohydrate" in name_lower:
        return "Carbohydrates"
    elif "energy" in name_lower or "calorie" in name_lower:
        return "Calories"
    elif "sugar" in name_lower and "total" in name_lower:
        return "Total Sugars"
    elif "fiber" in name_lower:
        return "Dietary Fiber"
    elif "sodium" in name_lower:
        return "Sodium"
    elif "calcium" in name_lower:
        return "Calcium"
    elif "iron" in name_lower:
        return "Iron"
    elif "vitamin c" in name_lower:
        return "Vitamin C"
    else:
        return nutrient_name  # Keep original if no mapping found

# --- Internal Helper Functions for Spoonacular Nutrition ---
def _spoonacular_get_nutrition_info(ingredient_list: List[str]) -> Dict[str, Any]:
    """
    Calls the Spoonacular API to fetch nutrition info for a list of ingredients.
    Returns a normalized nutrition dict.
    """
    url = f"https://api.spoonacular.com/recipes/parseIngredients"
    params = {
        "apiKey": SPOONACULAR_API_KEY,
    }
    nutrition = {}
    for ingredient in ingredient_list:
        ingredient_params = params.copy()
        ingredient_params["ingredientList"] = ingredient
        ingredient_params["servings"] = 1
        response = requests.post(url, data=ingredient_params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if isinstance(data, list) and data:
            n = data[0].get("nutrition", {})
            for k, v in n.items():
                if k not in nutrition:
                    nutrition[k] = v
                else:
                    # If numeric, sum values
                    try:
                        nutrition[k] += v
                    except Exception:
                        pass
    return {"nutrition": nutrition, "source": "spoonacular"}

# --- Normalization Functions ---
def _normalize_spoonacular_recipe(recipe: Dict[str, Any]) -> Dict[str, Any]:
    """
    Converts a Spoonacular API recipe dict to the app's standard format.
    """
    # Extract ingredients
    ingredients = []
    if recipe.get("extendedIngredients"):
        ingredients = [i.get("originalString", i.get("original", "")) if isinstance(i, dict) else str(i)
                      for i in recipe.get("extendedIngredients", [])]

    # Extract instructions - Spoonacular uses analyzedInstructions array
    instructions = []
    if recipe.get("analyzedInstructions"):
        for instruction_group in recipe.get("analyzedInstructions", []):
            if isinstance(instruction_group, dict) and instruction_group.get("steps"):
                for step in instruction_group.get("steps", []):
                    if isinstance(step, dict) and step.get("step"):
                        instructions.append(step.get("step"))
    elif recipe.get("instructions"):
        # Fallback to simple instructions field if available
        instructions_text = recipe.get("instructions")
        if isinstance(instructions_text, str):
            # Split by sentences or periods for better formatting
            instructions = [inst.strip() for inst in instructions_text.split('.') if inst.strip()]

    # Extract additional metadata
    ready_in_minutes = recipe.get("readyInMinutes")
    servings = recipe.get("servings")

    return {
        "id": recipe.get("id"),
        "title": recipe.get("title"),
        "image_url": recipe.get("image"),
        "ingredients": ingredients,
        "instructions": instructions,
        "nutrition": recipe.get("nutrition", {}),
        "source": "spoonacular",
        "readyInMinutes": ready_in_minutes,
        "servings": servings,
        "summary": recipe.get("summary", ""),
    }

def _tasty_get_recipe_details(recipe_id: str) -> Dict[str, Any]:
    """
    Calls the Tasty API to get full details for a single recipe by ID.
    Returns a normalized recipe dict.
    """
    url = f"https://{TASTY_API_HOST}/recipes/get-more-info"
    headers = {
        "x-rapidapi-host": TASTY_API_HOST,
        "x-rapidapi-key": TASTY_API_KEY,
    }
    params = {"id": recipe_id}
    response = requests.get(url, headers=headers, params=params, timeout=10)
    response.raise_for_status()
    recipe = response.json()
    return _normalize_tasty_recipe(recipe)

def _normalize_tasty_recipe(recipe: Dict[str, Any]) -> Dict[str, Any]:
    """
    Converts a Tasty API recipe dict to the app's standard format.
    """
    # Extract ingredients from sections
    ingredients = []
    if recipe.get("sections"):
        for section in recipe.get("sections", []):
            if section.get("components"):
                for component in section.get("components", []):
                    if component.get("raw_text"):
                        ingredients.append(component.get("raw_text"))

    # Extract instructions
    instructions = []
    if recipe.get("instructions"):
        for instruction in recipe.get("instructions", []):
            if instruction.get("display_text"):
                instructions.append(instruction.get("display_text"))

    return {
        "id": recipe.get("id"),
        "title": recipe.get("name"),
        "image_url": (recipe.get("thumbnail_url") or recipe.get("beauty_url")),
        "ingredients": ingredients,
        "instructions": instructions,
        "nutrition": recipe.get("nutrition", {}),
        "source": "tasty",
        "readyInMinutes": recipe.get("total_time_minutes"),
        "servings": recipe.get("num_servings"),
        "summary": recipe.get("description", ""),
    }

def _normalize_themealdb_recipe(recipe: Dict[str, Any]) -> Dict[str, Any]:
    """
    Converts a TheMealDB API recipe dict to the app's standard format.
    """
    # Extract ingredients and measurements
    ingredients = []
    for i in range(1, 21):  # TheMealDB has up to 20 ingredients
        ingredient = recipe.get(f"strIngredient{i}", "")
        measure = recipe.get(f"strMeasure{i}", "")

        if ingredient and ingredient.strip():
            if measure and measure.strip():
                ingredients.append(f"{measure.strip()} {ingredient.strip()}")
            else:
                ingredients.append(ingredient.strip())

    # Extract instructions (split by periods or newlines for better formatting)
    instructions_text = recipe.get("strInstructions", "")
    instructions = []
    if instructions_text:
        # Split by sentences or newlines, filter out empty ones
        instruction_parts = instructions_text.replace('\r\n', '\n').split('\n')
        instructions = [part.strip() for part in instruction_parts if part.strip()]

        # If no newlines, try splitting by periods for long instructions
        if len(instructions) == 1 and len(instructions[0]) > 200:
            sentence_parts = instructions[0].split('. ')
            instructions = [part.strip() + '.' for part in sentence_parts if part.strip()]

    return {
        "id": recipe.get("idMeal"),
        "title": recipe.get("strMeal"),
        "image_url": recipe.get("strMealThumb"),
        "ingredients": ingredients,
        "instructions": instructions,
        "nutrition": {},  # TheMealDB doesn't provide nutrition data
        "source": "themealdb",
        "category": recipe.get("strCategory"),
        "area": recipe.get("strArea"),
        "tags": recipe.get("strTags", "").split(",") if recipe.get("strTags") else [],
        "youtube_url": recipe.get("strYoutube"),
    }

def get_nutrition_info(ingredient_list: List[str], detailed: bool = True) -> Dict[str, Any]:
    """
    Enhanced nutrition info fetching using FoodData Central with comprehensive data processing.
    Falls back to Spoonacular if FoodData Central fails.
    Returns standardized nutrition data with optional detailed breakdown.

    Args:
        ingredient_list: List of ingredient strings
        detailed: Whether to include detailed nutritional breakdown

    Returns:
        Comprehensive nutrition data dictionary
    """
    # Filter out None values and empty strings
    valid_ingredients = [ing for ing in ingredient_list if ing and isinstance(ing, str) and ing.strip()]
    if not valid_ingredients:
        return {"nutrition": {}, "source": "none", "details": []}

    # Create a safe cache key by hashing the ingredients
    import hashlib
    ingredients_str = ','.join(sorted(valid_ingredients))
    cache_key = f"nutrition_v2:{hashlib.md5(ingredients_str.encode()).hexdigest()}:{detailed}"
    cached = safe_cache_get(cache_key)
    if cached is not None:
        logger.debug(f"Using cached nutrition data for {len(valid_ingredients)} ingredients")
        return cached

    # Try enhanced FoodData Central first
    try:
        nutrition = _fooddata_get_nutrition_info(valid_ingredients)
        if nutrition and nutrition.get("nutrition"):
            # Add nutritional analysis
            nutrition["analysis"] = _analyze_nutritional_content(nutrition["nutrition"])
            safe_cache_set(cache_key, nutrition, timeout=RECIPE_CACHE_DURATION)
            logger.info(f"Successfully retrieved FoodData nutrition for {nutrition.get('processed_ingredients', 0)} ingredients")
            return nutrition
    except Exception as e:
        logger.error(f"Enhanced FoodData Central nutrition failed: {e}")

    # Fallback to Spoonacular
    try:
        logger.info("Falling back to Spoonacular for nutrition data")
        nutrition = _spoonacular_get_nutrition_info(valid_ingredients)
        if nutrition and nutrition.get("nutrition"):
            # Standardize Spoonacular format to match FoodData format
            standardized_nutrition = {
                "nutrition": nutrition["nutrition"],
                "source": "spoonacular",
                "details": [],
                "processed_ingredients": len(valid_ingredients),
                "total_ingredients": len(valid_ingredients),
                "analysis": _analyze_nutritional_content(nutrition["nutrition"])
            }
            safe_cache_set(cache_key, standardized_nutrition, timeout=RECIPE_CACHE_DURATION)
            return standardized_nutrition
    except Exception as e:
        logger.error(f"Spoonacular nutrition fallback failed: {e}")

    # Return empty result if all methods fail
    return {
        "nutrition": {},
        "source": "none",
        "details": [],
        "processed_ingredients": 0,
        "total_ingredients": len(valid_ingredients),
        "analysis": {}
    }

def _analyze_nutritional_content(nutrition_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze nutritional content and provide insights.

    Args:
        nutrition_data: Dictionary of nutritional information

    Returns:
        Dictionary with nutritional analysis and insights
    """
    analysis = {
        "macronutrients": {},
        "vitamins_minerals": {},
        "health_indicators": {},
        "dietary_flags": []
    }

    try:
        # Extract key macronutrients
        calories = nutrition_data.get("Calories", {}).get("amount", 0)
        protein = nutrition_data.get("Protein", {}).get("amount", 0)
        carbs = nutrition_data.get("Carbohydrates", {}).get("amount", 0)
        fat = nutrition_data.get("Total Fat", {}).get("amount", 0)
        fiber = nutrition_data.get("Dietary Fiber", {}).get("amount", 0)
        sugar = nutrition_data.get("Total Sugars", {}).get("amount", 0)
        sodium = nutrition_data.get("Sodium", {}).get("amount", 0)

        # Calculate macronutrient percentages
        if calories > 0:
            protein_calories = protein * 4
            carb_calories = carbs * 4
            fat_calories = fat * 9

            analysis["macronutrients"] = {
                "protein_percentage": round((protein_calories / calories) * 100, 1),
                "carb_percentage": round((carb_calories / calories) * 100, 1),
                "fat_percentage": round((fat_calories / calories) * 100, 1),
                "calories_per_serving": round(calories, 0)
            }

        # Analyze vitamins and minerals
        vitamin_c = nutrition_data.get("Vitamin C", {}).get("amount", 0)
        calcium = nutrition_data.get("Calcium", {}).get("amount", 0)
        iron = nutrition_data.get("Iron", {}).get("amount", 0)

        analysis["vitamins_minerals"] = {
            "vitamin_c_mg": round(vitamin_c, 1),
            "calcium_mg": round(calcium, 1),
            "iron_mg": round(iron, 2)
        }

        # Health indicators
        analysis["health_indicators"] = {
            "fiber_grams": round(fiber, 1),
            "sugar_grams": round(sugar, 1),
            "sodium_mg": round(sodium, 1),
            "protein_grams": round(protein, 1)
        }

        # Dietary flags
        if sodium > 2300:  # High sodium (daily limit)
            analysis["dietary_flags"].append("high_sodium")
        if fiber >= 5:  # Good source of fiber
            analysis["dietary_flags"].append("high_fiber")
        if protein >= 20:  # High protein
            analysis["dietary_flags"].append("high_protein")
        if sugar > 25:  # High sugar
            analysis["dietary_flags"].append("high_sugar")
        if fat < calories * 0.2 / 9:  # Low fat (less than 20% of calories)
            analysis["dietary_flags"].append("low_fat")

    except Exception as e:
        logger.warning(f"Nutritional analysis failed: {e}")
        analysis["error"] = str(e)

    return analysis

def filter_recipes_by_nutrition(recipes: List[Dict[str, Any]], nutrition_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Filter recipes based on nutritional criteria.

    Args:
        recipes: List of recipe dictionaries
        nutrition_filters: Dictionary of nutritional filter criteria

    Returns:
        Filtered list of recipes meeting nutritional criteria
    """
    if not nutrition_filters:
        return recipes

    filtered_recipes = []

    for recipe in recipes:
        try:
            # Get nutrition info for recipe if not already present
            nutrition_data = recipe.get("nutrition_analysis")
            if not nutrition_data and recipe.get("ingredients"):
                nutrition_info = get_nutrition_info(recipe["ingredients"], detailed=True)
                nutrition_data = nutrition_info.get("analysis", {})
                recipe["nutrition_analysis"] = nutrition_data

            if not nutrition_data:
                continue  # Skip recipes without nutrition data

            # Apply filters
            meets_criteria = True

            # Calorie filters
            if "max_calories" in nutrition_filters:
                calories = nutrition_data.get("macronutrients", {}).get("calories_per_serving", 0)
                if calories > nutrition_filters["max_calories"]:
                    meets_criteria = False

            if "min_protein" in nutrition_filters:
                protein = nutrition_data.get("health_indicators", {}).get("protein_grams", 0)
                if protein < nutrition_filters["min_protein"]:
                    meets_criteria = False

            if "max_sodium" in nutrition_filters:
                sodium = nutrition_data.get("health_indicators", {}).get("sodium_mg", 0)
                if sodium > nutrition_filters["max_sodium"]:
                    meets_criteria = False

            if "min_fiber" in nutrition_filters:
                fiber = nutrition_data.get("health_indicators", {}).get("fiber_grams", 0)
                if fiber < nutrition_filters["min_fiber"]:
                    meets_criteria = False

            # Dietary flag filters
            if "required_flags" in nutrition_filters:
                recipe_flags = nutrition_data.get("dietary_flags", [])
                for required_flag in nutrition_filters["required_flags"]:
                    if required_flag not in recipe_flags:
                        meets_criteria = False
                        break

            if "excluded_flags" in nutrition_filters:
                recipe_flags = nutrition_data.get("dietary_flags", [])
                for excluded_flag in nutrition_filters["excluded_flags"]:
                    if excluded_flag in recipe_flags:
                        meets_criteria = False
                        break

            if meets_criteria:
                filtered_recipes.append(recipe)

        except Exception as e:
            logger.warning(f"Failed to apply nutrition filter to recipe: {e}")
            continue

    logger.info(f"Filtered {len(recipes)} recipes to {len(filtered_recipes)} based on nutrition criteria")
    return filtered_recipes

def enrich_recipes_with_nutrition(recipes: List[Dict[str, Any]], include_analysis: bool = True) -> List[Dict[str, Any]]:
    """
    Enrich recipe data with comprehensive nutritional information.

    Args:
        recipes: List of recipe dictionaries
        include_analysis: Whether to include nutritional analysis

    Returns:
        List of recipes enriched with nutritional data
    """
    enriched_recipes = []

    for recipe in recipes:
        try:
            # Skip if nutrition data already exists and is comprehensive
            if recipe.get("nutrition_analysis") and include_analysis:
                enriched_recipes.append(recipe)
                continue

            # Get ingredients for nutrition lookup
            ingredients = recipe.get("ingredients", [])
            if not ingredients:
                enriched_recipes.append(recipe)
                continue

            # Fetch comprehensive nutrition data
            nutrition_info = get_nutrition_info(ingredients, detailed=include_analysis)

            # Add nutrition data to recipe
            recipe["nutrition_data"] = nutrition_info.get("nutrition", {})
            recipe["nutrition_source"] = nutrition_info.get("source", "none")

            if include_analysis:
                recipe["nutrition_analysis"] = nutrition_info.get("analysis", {})
                recipe["nutrition_details"] = nutrition_info.get("details", [])

            enriched_recipes.append(recipe)

        except Exception as e:
            logger.warning(f"Failed to enrich recipe with nutrition data: {e}")
            enriched_recipes.append(recipe)  # Include recipe even if nutrition fails

    logger.info(f"Enriched {len(enriched_recipes)} recipes with nutritional data")
    return enriched_recipes

def comprehensive_recipe_search(query: str, filters: Optional[Dict[str, Any]] = None,
                              include_nutrition: bool = True, nutrition_filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Comprehensive recipe search with enhanced API fallback system and nutritional analysis.

    Args:
        query: Search query string
        filters: Optional search filters
        include_nutrition: Whether to include nutritional analysis
        nutrition_filters: Optional nutritional filtering criteria

    Returns:
        Comprehensive search results with metadata
    """
    search_start_time = time.time()

    try:
        # Perform initial recipe search with universal fallback
        recipes = search_recipes(query, filters)

        if not recipes:
            return {
                "recipes": [],
                "total_found": 0,
                "search_time": round(time.time() - search_start_time, 2),
                "apis_used": [],
                "nutrition_included": False,
                "filters_applied": bool(nutrition_filters)
            }

        # Track which APIs were used
        apis_used = list(set([recipe.get("source", "unknown") for recipe in recipes]))

        # Enrich with nutritional data if requested
        if include_nutrition:
            recipes = enrich_recipes_with_nutrition(recipes, include_analysis=True)

        # Apply nutritional filters if provided
        if nutrition_filters:
            recipes = filter_recipes_by_nutrition(recipes, nutrition_filters)

        search_time = round(time.time() - search_start_time, 2)

        return {
            "recipes": recipes,
            "total_found": len(recipes),
            "search_time": search_time,
            "apis_used": apis_used,
            "nutrition_included": include_nutrition,
            "filters_applied": bool(nutrition_filters),
            "query": query,
            "filters": filters or {},
            "nutrition_filters": nutrition_filters or {}
        }

    except Exception as e:
        logger.error(f"Comprehensive recipe search failed: {e}")
        return {
            "recipes": [],
            "total_found": 0,
            "search_time": round(time.time() - search_start_time, 2),
            "apis_used": [],
            "nutrition_included": False,
            "filters_applied": False,
            "error": str(e)
        }

def get_api_health_status() -> Dict[str, Any]:
    """
    Check the health status of all configured APIs.

    Returns:
        Dictionary with health status of each API
    """
    health_status = {
        "timestamp": time.time(),
        "apis": {}
    }

    # Test Spoonacular API
    try:
        if SPOONACULAR_API_KEY:
            test_url = "https://api.spoonacular.com/recipes/complexSearch"
            test_params = {"apiKey": SPOONACULAR_API_KEY, "query": "test", "number": 1}
            response = requests.get(test_url, params=test_params, timeout=5)
            health_status["apis"]["spoonacular"] = {
                "available": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
        else:
            health_status["apis"]["spoonacular"] = {"available": False, "error": "API key not configured"}
    except Exception as e:
        health_status["apis"]["spoonacular"] = {"available": False, "error": str(e)}

    # Test Tasty API
    try:
        if TASTY_API_HOST and TASTY_API_KEY:
            test_url = f"https://{TASTY_API_HOST}/recipes/list"
            test_headers = {"x-rapidapi-host": TASTY_API_HOST, "x-rapidapi-key": TASTY_API_KEY}
            test_params = {"q": "test", "size": 1}
            response = requests.get(test_url, headers=test_headers, params=test_params, timeout=5)
            health_status["apis"]["tasty"] = {
                "available": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
        else:
            health_status["apis"]["tasty"] = {"available": False, "error": "API credentials not configured"}
    except Exception as e:
        health_status["apis"]["tasty"] = {"available": False, "error": str(e)}

    # Test FoodData Central API
    try:
        if FOODDATA_API_KEY:
            test_url = "https://api.nal.usda.gov/fdc/v1/foods/search"
            test_params = {"api_key": FOODDATA_API_KEY, "query": "apple", "pageSize": 1}
            response = requests.get(test_url, params=test_params, timeout=5)
            health_status["apis"]["fooddata"] = {
                "available": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
        else:
            health_status["apis"]["fooddata"] = {"available": False, "error": "API key not configured"}
    except Exception as e:
        health_status["apis"]["fooddata"] = {"available": False, "error": str(e)}

    # Test TheMealDB API (free, no key required)
    try:
        test_url = f"{THEMEALDB_BASE_URL}/search.php"
        test_params = {"s": "test"}
        response = requests.get(test_url, params=test_params, timeout=5)
        health_status["apis"]["themealdb"] = {
            "available": response.status_code == 200,
            "status_code": response.status_code,
            "response_time": response.elapsed.total_seconds()
        }
    except Exception as e:
        health_status["apis"]["themealdb"] = {"available": False, "error": str(e)}

    # Calculate overall health
    available_apis = sum(1 for api_status in health_status["apis"].values() if api_status.get("available", False))
    total_apis = len(health_status["apis"])
    health_status["overall_health"] = {
        "available_apis": available_apis,
        "total_apis": total_apis,
        "health_percentage": round((available_apis / total_apis) * 100, 1) if total_apis > 0 else 0
    }

    return health_status

# --- New Search Functions for Different Search Types ---

def search_recipes_by_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by name/title using Spoonacular API with Tasty and TheMealDB fallbacks.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_name", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try Spoonacular first
    try:
        results = _spoonacular_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        # Check if it's a payment error (402) and log appropriately
        if hasattr(e, 'response') and e.response.status_code == 402:
            logger.warning("Spoonacular API quota exceeded (402) in name search. Falling back to Tasty API.")
        else:
            logger.error(f"Spoonacular name search failed: {e}")

    # Universal fallback to Tasty API
    try:
        results = _spoonacular_to_tasty_fallback(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Tasty API fallback failed: {e}")

    # Final fallback to TheMealDB
    try:
        results = _themealdb_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB name search failed: {e}")

    return []

def search_recipes_by_cuisine(cuisine: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by cuisine using TheMealDB first, then Spoonacular fallback.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_cuisine", cuisine, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try TheMealDB first (excellent for cuisine searches)
    try:
        results = _themealdb_search_by_cuisine(cuisine, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB cuisine search failed: {e}")

    # Fallback to Spoonacular
    try:
        results = _spoonacular_search_by_cuisine(cuisine, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        # Check if it's a payment error (402) and log appropriately
        if hasattr(e, 'response') and e.response.status_code == 402:
            logger.warning("Spoonacular API quota exceeded (402) in cuisine search.")
        else:
            logger.error(f"Spoonacular cuisine search failed: {e}")

    return []

def search_recipes_by_diet(diet_restrictions: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by dietary restrictions using Spoonacular API with Tasty fallback.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_diet", diet_restrictions, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try Spoonacular first
    try:
        results = _spoonacular_search_by_diet(diet_restrictions, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Spoonacular diet search failed: {e}")
        if hasattr(e, 'response') and e.response.status_code == 402:
            logger.warning("Spoonacular API quota exceeded (402) in diet search. Falling back to Tasty API.")

    # Universal fallback to Tasty API with diet-based query modification
    try:
        # Modify query to include diet restrictions for better Tasty API results
        diet_query = f"{diet_restrictions} recipes"
        modified_filters = filters.copy() if filters else {}
        modified_filters['diet_query'] = diet_restrictions

        results = _spoonacular_to_tasty_fallback(diet_query, modified_filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Tasty API diet search fallback failed: {e}")

    return []

def search_recipes_by_time(time_input: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by cooking time using Spoonacular API with Tasty fallback.
    Parses time input like "30 minutes", "under 1 hour", "quick", etc.
    Returns standardized recipe list.
    """
    # Parse time input to get max_time in minutes
    max_time = _parse_time_input(time_input)
    if max_time is None:
        logger.warning(f"Could not parse time input: {time_input}")
        return []

    cache_key = _generate_cache_key("search_by_time", str(max_time), filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try Spoonacular first
    try:
        results = _spoonacular_search_by_time(max_time, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Spoonacular time search failed: {e}")
        if hasattr(e, 'response') and e.response.status_code == 402:
            logger.warning("Spoonacular API quota exceeded (402) in time search. Falling back to Tasty API.")

    # Universal fallback to Tasty API with time-based query modification
    try:
        # Create time-based query for Tasty API
        if max_time <= 30:
            time_query = "quick easy recipes"
        elif max_time <= 60:
            time_query = "30 minute recipes"
        else:
            time_query = "easy recipes"

        modified_filters = filters.copy() if filters else {}
        modified_filters['max_time'] = max_time

        results = _spoonacular_to_tasty_fallback(time_query, modified_filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Tasty API time search fallback failed: {e}")

    return []

def _parse_time_input(time_input: str) -> Optional[int]:
    """
    Parse time input string and return max time in minutes.
    Examples: "30 minutes", "1 hour", "under 45 minutes", "quick", "fast"
    """
    import re

    time_input_lower = time_input.lower().strip()

    # Handle common keywords
    if any(keyword in time_input_lower for keyword in ['quick', 'fast', 'rapid']):
        return 30
    elif any(keyword in time_input_lower for keyword in ['slow', 'long']):
        return 180

    # Extract numbers and time units
    # Look for patterns like "30 minutes", "1 hour", "45 min", etc.
    patterns = [
        r'(\d+)\s*(?:hours?|hrs?|h)\s*(?:(\d+)\s*(?:minutes?|mins?|m))?',  # "1 hour 30 minutes" or "2 hours"
        r'(\d+)\s*(?:minutes?|mins?|m)',  # "30 minutes"
        r'under\s+(\d+)\s*(?:minutes?|mins?|m)',  # "under 30 minutes"
        r'under\s+(\d+)\s*(?:hours?|hrs?|h)',  # "under 1 hour"
        r'(\d+)',  # Just a number, assume minutes
    ]

    for pattern in patterns:
        match = re.search(pattern, time_input_lower)
        if match:
            if 'hour' in pattern:
                if match.group(2):  # Has both hours and minutes
                    hours = int(match.group(1))
                    minutes = int(match.group(2))
                    return hours * 60 + minutes
                else:  # Just hours
                    hours = int(match.group(1))
                    return hours * 60
            else:  # Minutes
                return int(match.group(1))

    # Default fallback
    return None

# --- Public TheMealDB Search Functions ---
def search_recipes_by_themealdb_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by name using TheMealDB API.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("themealdb_name", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _themealdb_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB name search failed: {e}")
    return []

def search_recipes_by_themealdb_cuisine(area: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by cuisine/area using TheMealDB API.
    Returns standardized recipe list.
    """
    cache_key = _generate_cache_key("themealdb_cuisine", area, filters)
    cached = cache.get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _themealdb_search_by_cuisine(area, filters)
        if results:
            cache.set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB cuisine search failed: {e}")
    return []

def get_random_recipes(count: int = 5) -> List[Dict[str, Any]]:
    """
    Get random recipes using TheMealDB API.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = f"themealdb_random:{count}"
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _themealdb_get_random_meals(count)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB random search failed: {e}")
    return []

def _spoonacular_search_by_meal_type(meal_type: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by meal type using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "type": meal_type,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']

        logger.debug(f"Calling Spoonacular API for meal type search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular meal type search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_meal_type: {str(e)}", exc_info=True)
        raise

def search_recipes_by_meal_type(meal_type: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by meal type using Spoonacular API.
    Meal types: breakfast, lunch, dinner, snack, dessert, appetizer, main course, side dish, etc.
    Returns standardized recipe list with robust Redis caching.
    """
    # Normalize meal type input
    meal_type_lower = meal_type.lower().strip()

    # Map common meal type variations to Spoonacular's expected values
    meal_type_mapping = {
        'breakfast': 'breakfast',
        'lunch': 'main course',
        'dinner': 'main course',
        'snack': 'snack',
        'dessert': 'dessert',
        'appetizer': 'appetizer',
        'main course': 'main course',
        'side dish': 'side dish',
        'salad': 'salad',
        'soup': 'soup',
        'beverage': 'beverage',
        'drink': 'beverage',
        'bread': 'bread',
        'sauce': 'sauce',
        'marinade': 'marinade',
        'fingerfood': 'fingerfood'
    }

    # Get the standardized meal type
    standardized_meal_type = meal_type_mapping.get(meal_type_lower, meal_type_lower)

    cache_key = _generate_cache_key("search_by_meal_type", standardized_meal_type, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _spoonacular_search_by_meal_type(standardized_meal_type, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Spoonacular meal type search failed: {e}")
    return []

def _spoonacular_search_by_skill_level(skill_level: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by skill level using Spoonacular complexSearch endpoint.
    Uses cooking time and complexity as proxies for skill level.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Map skill levels to time constraints and sorting
        skill_level_lower = skill_level.lower().strip()

        if skill_level_lower in ['beginner', 'easy', 'simple', 'quick']:
            # Beginner: Quick recipes (under 30 minutes), sorted by time
            params['maxReadyTime'] = 30
            params['sort'] = 'time'
            params['sortDirection'] = 'asc'
        elif skill_level_lower in ['intermediate', 'medium', 'moderate']:
            # Intermediate: Medium time recipes (30-60 minutes)
            params['minReadyTime'] = 30
            params['maxReadyTime'] = 60
            params['sort'] = 'popularity'
        elif skill_level_lower in ['advanced', 'expert', 'hard', 'difficult', 'complex']:
            # Advanced: Longer recipes (over 60 minutes), more complex
            params['minReadyTime'] = 60
            params['sort'] = 'time'
            params['sortDirection'] = 'desc'
        else:
            # Default to intermediate if skill level not recognized
            params['maxReadyTime'] = 60
            params['sort'] = 'popularity'

        # Apply additional filters if provided
        if filters:
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']

        logger.debug(f"Calling Spoonacular API for skill level search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular skill level search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_skill_level: {str(e)}", exc_info=True)
        raise

def search_recipes_by_skill_level(skill_level: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by skill level using Spoonacular API.
    Skill levels: beginner, intermediate, advanced (uses cooking time as proxy for difficulty).
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_skill_level", skill_level, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _spoonacular_search_by_skill_level(skill_level, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Spoonacular skill level search failed: {e}")
    return []

def _spoonacular_search_by_videos(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes with video instructions using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "query": query if query else "",
            "number": 20,
            "addRecipeInformation": True,
            "addRecipeInstructions": True,
            "instructionsRequired": True,
            "sort": "popularity",
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']

        logger.debug(f"Calling Spoonacular API for video search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular video search")

        # Filter for recipes that have video content
        video_recipes = []
        for recipe in recipes:
            normalized_recipe = _normalize_spoonacular_recipe(recipe)

            # Check if recipe has video content in various fields
            has_video = False

            # Check if there's a video URL in the source URL or instructions
            if recipe.get('sourceUrl'):
                video_indicators = ['youtube', 'vimeo', 'video', 'watch']
                if any(indicator in recipe['sourceUrl'].lower() for indicator in video_indicators):
                    has_video = True

            # Check if instructions mention video
            if recipe.get('instructions'):
                for instruction in recipe['instructions']:
                    if isinstance(instruction, dict) and instruction.get('step'):
                        step_text = instruction['step'].lower()
                        if any(word in step_text for word in ['video', 'watch', 'youtube']):
                            has_video = True
                            break

            # Check if recipe has analyzedInstructions with equipment that might indicate video
            if recipe.get('analyzedInstructions'):
                for instruction_set in recipe['analyzedInstructions']:
                    if instruction_set.get('steps'):
                        for step in instruction_set['steps']:
                            if step.get('equipment'):
                                for equipment in step['equipment']:
                                    if equipment.get('name') and 'camera' in equipment['name'].lower():
                                        has_video = True
                                        break

            # For now, include recipes with detailed instructions as they're more likely to have videos
            # This is a fallback since Spoonacular doesn't directly indicate video presence
            if has_video or (recipe.get('instructions') and len(recipe['instructions']) > 3):
                video_recipes.append(normalized_recipe)

        logger.debug(f"Filtered to {len(video_recipes)} recipes with potential video content")
        return video_recipes

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_videos: {str(e)}", exc_info=True)
        raise

def _themealdb_search_by_videos(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes with video instructions using TheMealDB API.
    TheMealDB has YouTube video links for many recipes.
    """
    try:
        # If query is provided, search by name first
        if query and query.strip():
            url = f"{THEMEALDB_BASE_URL}/search.php"
            params = {"s": query}
        else:
            # Get random meals if no query
            url = f"{THEMEALDB_BASE_URL}/random.php"
            params = {}

        logger.debug(f"Calling TheMealDB API for video search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []

        # Filter for meals that have YouTube videos
        video_meals = []
        for meal in meals:
            if meal.get('strYoutube') and meal['strYoutube'].strip():
                video_meals.append(_normalize_themealdb_recipe(meal))

        logger.debug(f"Found {len(video_meals)} meals with YouTube videos from TheMealDB")
        return video_meals

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_videos: {str(e)}", exc_info=True)
        raise

def search_recipes_by_videos(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes that include video instructions.
    Uses both Spoonacular and TheMealDB APIs to find recipes with video content.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_videos", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    results = []

    try:
        # Try TheMealDB first as it has explicit YouTube video links
        themealdb_results = _themealdb_search_by_videos(query, filters)
        results.extend(themealdb_results)
        logger.debug(f"TheMealDB video search returned {len(themealdb_results)} results")

        # If we need more results, try Spoonacular
        if len(results) < 15:
            try:
                spoonacular_results = _spoonacular_search_by_videos(query, filters)
                # Avoid duplicates by checking titles
                existing_titles = {r.get('title', '').lower() for r in results}
                new_spoonacular = [r for r in spoonacular_results
                                 if r.get('title', '').lower() not in existing_titles]
                results.extend(new_spoonacular)
                logger.debug(f"Spoonacular video search added {len(new_spoonacular)} new results")
            except Exception as e:
                logger.error(f"Spoonacular video search failed: {e}")

        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results

    except Exception as e:
        logger.error(f"Video search failed: {e}")

    return []

def _apply_quick_filter_params(filters: Dict[str, Any], quick_filter: str) -> Dict[str, Any]:
    """
    Apply quick filter specific parameters to the filters dictionary.

    Args:
        filters: Existing filters dictionary
        quick_filter: Quick filter type ('Trending', 'Quick & Easy', 'Budget', 'Family')

    Returns:
        Updated filters dictionary with quick filter parameters
    """
    if not quick_filter or quick_filter == 'All':
        return filters

    # Create a copy to avoid modifying the original
    updated_filters = dict(filters) if filters else {}

    if quick_filter == 'Trending':
        # For trending recipes, we can use tags or sort by popularity
        updated_filters['sort'] = 'popularity'
        updated_filters['tags'] = updated_filters.get('tags', []) + ['popular', 'trending']

    elif quick_filter == 'Quick & Easy':
        # For quick recipes, limit cooking time
        updated_filters['maxReadyTime'] = 30  # 30 minutes max
        updated_filters['tags'] = updated_filters.get('tags', []) + ['quick', 'easy', 'simple']

    elif quick_filter == 'Budget':
        # For budget recipes, look for cheap/affordable tags
        updated_filters['tags'] = updated_filters.get('tags', []) + ['cheap', 'budget', 'affordable', 'economical']
        updated_filters['sort'] = 'price'  # If API supports price sorting

    elif quick_filter == 'Family':
        # For family recipes, look for family-friendly tags
        updated_filters['tags'] = updated_filters.get('tags', []) + ['family', 'kid-friendly', 'family-friendly']
        updated_filters['excludeIngredients'] = updated_filters.get('excludeIngredients', []) + ['alcohol', 'wine', 'beer']

    return updated_filters


def unified_search(search_type: str, query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent unified search function that strategically routes to appropriate APIs based on their strengths.

    API Strategy:
    - TheMealDB: Primary for cuisine searches, good for international recipes (free, reliable)
    - Spoonacular: Primary for dietary restrictions, nutrition data, detailed recipe info
    - Tasty: Limited to 10 requests/day, use for trending/popular recipes when available

    Args:
        search_type: One of 'name', 'ingredients', 'cuisine', 'dietary', 'time', 'meal_type', 'skill_level', 'videos'
        query: The search query string
        filters: Optional additional filters

    Returns:
        List of normalized recipe dictionaries
    """
    try:
        # Apply quick filter parameters if present
        if filters and 'quick_filter' in filters:
            quick_filter = filters.pop('quick_filter')  # Remove from filters to avoid passing to APIs
            filters = _apply_quick_filter_params(filters, quick_filter)
            logger.debug(f"Applied quick filter '{quick_filter}' to search parameters")

        results = []

        if search_type == 'name':
            # Strategy: Try TheMealDB first (free), then Spoonacular, then Tasty if available
            results = _intelligent_name_search(query, filters)

        elif search_type == 'ingredients':
            # Strategy: Try TheMealDB for single ingredient, then existing multi-API approach
            results = _intelligent_ingredient_search(query, filters)

        elif search_type == 'cuisine':
            # Strategy: TheMealDB is excellent for cuisine searches, use as primary
            results = _intelligent_cuisine_search(query, filters)

        elif search_type == 'dietary':
            # Strategy: Spoonacular is best for dietary restrictions
            results = search_recipes_by_diet(query, filters)

        elif search_type == 'time':
            # Strategy: Spoonacular only (TheMealDB doesn't have time data)
            results = search_recipes_by_time(query, filters)

        elif search_type == 'meal_type':
            # Strategy: Spoonacular is best for meal type searches
            results = search_recipes_by_meal_type(query, filters)

        elif search_type == 'skill_level':
            # Strategy: Spoonacular only (uses time and complexity as proxies)
            results = search_recipes_by_skill_level(query, filters)

        elif search_type == 'videos':
            # Strategy: TheMealDB first (has explicit YouTube links), then Spoonacular
            results = search_recipes_by_videos(query, filters)

        else:
            logger.warning(f"Unknown search type: {search_type}")
            # Default to intelligent name search
            results = _intelligent_name_search(query, filters)

        logger.debug(f"Unified search for {search_type}:'{query}' returned {len(results)} results")
        return results

    except Exception as e:
        logger.error(f"Unified search failed for type {search_type}, query {query}: {e}")
        return []

def _intelligent_name_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent name search using multiple APIs strategically.
    Now includes caching to prevent repeated API calls.
    """
    # Add caching to prevent repeated API calls
    cache_key = _generate_cache_key("intelligent_name_search", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        logger.debug(f"Returning cached intelligent name search results for '{query}'")
        return cached

    results = []

    try:
        # 1. Try TheMealDB first (free, good coverage)
        themealdb_results = _themealdb_search_by_name(query, filters)
        results.extend(themealdb_results)
        logger.debug(f"TheMealDB name search returned {len(themealdb_results)} results")

        # 2. Try Spoonacular for more diverse results (handle payment errors gracefully)
        try:
            spoonacular_results = _spoonacular_search_by_name(query, filters)
            # Avoid duplicates by checking titles
            existing_titles = {r.get('title', '').lower() for r in results}
            new_spoonacular = [r for r in spoonacular_results
                             if r.get('title', '').lower() not in existing_titles]
            results.extend(new_spoonacular)
            logger.debug(f"Spoonacular added {len(new_spoonacular)} new results")
        except Exception as e:
            # Check if it's a payment error (402) and log appropriately
            if hasattr(e, 'response') and e.response.status_code == 402:
                logger.warning("Spoonacular API quota exceeded (402) in intelligent name search. Continuing with other sources.")
            else:
                logger.error(f"Spoonacular API failed in intelligent name search: {e}")

        # 3. If still need more, try Tasty API (no daily limits)
        if len(results) < 25:
            try:
                tasty_results = _tasty_search_recipes(query, filters)
                existing_titles = {r.get('title', '').lower() for r in results}
                new_tasty = [r for r in tasty_results
                            if r.get('title', '').lower() not in existing_titles]
                results.extend(new_tasty)
                logger.debug(f"Tasty added {len(new_tasty)} new results")
            except Exception as e:
                logger.error(f"Tasty API failed in intelligent name search: {e}")

    except Exception as e:
        logger.error(f"Error in intelligent name search: {e}")

    final_results = results[:30]  # Increased limit to 30 results

    # Cache the results for 1 day
    safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
    logger.debug(f"Cached intelligent name search results for '{query}' with {len(final_results)} recipes")

    return final_results

def get_additional_spoonacular_results(query: str, search_type: str, existing_recipes: List[Dict[str, Any]], limit: int = 20) -> List[Dict[str, Any]]:
    """
    Get additional diverse results from Spoonacular API for masonry grid.
    Uses different search strategies to get more varied results.
    Results are cached to ensure consistency.
    """
    # Generate cache key for additional results
    existing_ids = sorted([f"{r.get('source', '')}:{r.get('id', '')}" for r in existing_recipes if r.get('id')])
    existing_hash = hashlib.md5(json.dumps(existing_ids, sort_keys=True).encode()).hexdigest()[:16]

    cache_key = f"additional_results:{search_type}:{query}:{existing_hash}:{limit}"

    # Check cache first
    cached_results = safe_cache_get(cache_key)
    if cached_results is not None:
        logger.debug(f"Using cached additional results for key: {cache_key}")
        return cached_results

    additional_results = []
    existing_titles = {r.get('title', '').lower() for r in existing_recipes}

    try:
        # Strategy 1: Use broader search terms
        if search_type == 'name':
            # Extract key words from the query for broader search
            keywords = query.split()
            if len(keywords) > 1:
                # Try searching with individual keywords
                for keyword in keywords[:2]:  # Limit to first 2 keywords
                    if len(keyword) > 3:  # Only meaningful words
                        broader_results = _spoonacular_search_by_name(keyword, {'number': 10})
                        new_results = [r for r in broader_results
                                     if r.get('title', '').lower() not in existing_titles]
                        additional_results.extend(new_results)
                        existing_titles.update(r.get('title', '').lower() for r in new_results)

                        if len(additional_results) >= limit:
                            break

        # Strategy 2: Get popular/trending recipes if we still need more
        if len(additional_results) < limit:
            try:
                popular_results = _spoonacular_search_by_name('popular', {'number': 15, 'sort': 'popularity'})
                new_popular = [r for r in popular_results
                             if r.get('title', '').lower() not in existing_titles]
                additional_results.extend(new_popular)
                existing_titles.update(r.get('title', '').lower() for r in new_popular)
            except Exception as e:
                logger.debug(f"Popular recipes search failed: {e}")

        # Strategy 3: Diverse recipes with deterministic sorting if still need more
        if len(additional_results) < limit:
            try:
                # Use popularity sorting instead of random for consistent results
                diverse_results = _spoonacular_search_by_name('healthy', {'number': 20, 'sort': 'popularity'})
                new_diverse = [r for r in diverse_results
                             if r.get('title', '').lower() not in existing_titles]
                additional_results.extend(new_diverse)
            except Exception as e:
                logger.debug(f"Diverse recipes search failed: {e}")

        # Limit results and cache them
        final_results = additional_results[:limit]

        # Cache the results for 1 day (same as recipe data)
        safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
        logger.debug(f"Cached additional results with key: {cache_key}")

        logger.debug(f"Got {len(final_results)} additional Spoonacular results")
        return final_results

    except Exception as e:
        logger.error(f"Error getting additional Spoonacular results: {e}")
        return []














def _restricted_ingredient_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes that use ONLY the specified ingredients.
    This is a more restrictive search that filters out recipes with additional ingredients.

    Strategy:
    - Parse the user's ingredient list
    - Search for recipes using normal ingredient search
    - Filter results to only include recipes that don't have extra ingredients
    """
    try:
        # Parse ingredients from query
        user_ingredients = [ing.strip().lower() for ing in query.split(',') if ing.strip()]
        if not user_ingredients:
            return []

        logger.debug(f"Restricted search for ingredients: {user_ingredients}")

        # Get all recipes that contain these ingredients (using normal search)
        temp_filters = dict(filters) if filters else {}
        temp_filters.pop('restrict_ingredients', None)  # Remove restriction flag for the base search

        all_recipes = []

        # Try TheMealDB first for single ingredients
        if len(user_ingredients) == 1:
            try:
                themealdb_results = _themealdb_search_by_ingredient(user_ingredients[0], temp_filters)
                all_recipes.extend(themealdb_results)
            except Exception as e:
                logger.error(f"TheMealDB restricted search failed: {e}")

        # Try Spoonacular for multi-ingredient or additional results
        try:
            spoonacular_results = _spoonacular_search_recipes(query, temp_filters)
            existing_titles = {r.get('title', '').lower() for r in all_recipes}
            new_spoonacular = [r for r in spoonacular_results
                             if r.get('title', '').lower() not in existing_titles]
            all_recipes.extend(new_spoonacular)
        except Exception as e:
            logger.error(f"Spoonacular restricted search failed: {e}")

        # Filter recipes to only include those with matching ingredients
        restricted_recipes = []
        for recipe in all_recipes:
            recipe_ingredients = recipe.get('ingredients', [])
            if not recipe_ingredients:
                # If no ingredients data, include the recipe (benefit of doubt)
                restricted_recipes.append(recipe)
                continue

            # Convert recipe ingredients to lowercase for comparison
            recipe_ingredient_names = []
            for ingredient in recipe_ingredients:
                if isinstance(ingredient, str):
                    recipe_ingredient_names.append(ingredient.lower())
                elif isinstance(ingredient, dict):
                    name = ingredient.get('name', '') or ingredient.get('original', '')
                    if name:
                        recipe_ingredient_names.append(name.lower())

            # Check if recipe uses only the specified ingredients
            if _recipe_uses_only_specified_ingredients(recipe_ingredient_names, user_ingredients):
                restricted_recipes.append(recipe)
            elif len(recipe_ingredient_names) <= len(user_ingredients) + 3:
                # If recipe has few ingredients (user ingredients + max 3 common ones), include it
                restricted_recipes.append(recipe)

        logger.debug(f"Restricted ingredient search found {len(restricted_recipes)} recipes from {len(all_recipes)} total")
        return restricted_recipes[:20]  # Limit results

    except Exception as e:
        logger.error(f"Error in restricted ingredient search: {e}")
        return []


def _recipe_uses_only_specified_ingredients(recipe_ingredients: List[str], user_ingredients: List[str]) -> bool:
    """
    Check if a recipe uses only the ingredients specified by the user.
    This is a simplified check that looks for key ingredient matches.
    """
    try:
        # Common ingredients that are usually acceptable in "restricted" searches
        common_acceptable = {
            'salt', 'pepper', 'water', 'oil', 'olive oil', 'vegetable oil',
            'garlic', 'onion', 'black pepper', 'white pepper', 'sea salt',
            'cooking oil', 'butter', 'margarine', 'lemon', 'lime', 'herbs',
            'spices', 'seasoning', 'bay leaves', 'thyme', 'parsley', 'cilantro'
        }

        for recipe_ingredient in recipe_ingredients:
            recipe_ingredient = recipe_ingredient.lower().strip()

            # Skip empty ingredients
            if not recipe_ingredient:
                continue

            # Skip common acceptable ingredients
            if any(common in recipe_ingredient for common in common_acceptable):
                continue

            # Check if this recipe ingredient matches any user ingredient
            ingredient_found = False
            for user_ingredient in user_ingredients:
                if user_ingredient in recipe_ingredient or recipe_ingredient in user_ingredient:
                    ingredient_found = True
                    break

            # If we found an ingredient that's not in the user's list and not common, reject this recipe
            if not ingredient_found:
                return False

        return True

    except Exception as e:
        logger.error(f"Error checking ingredient restriction: {e}")
        return False


def _intelligent_ingredient_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent ingredient search using multiple APIs strategically.
    Now includes caching to prevent repeated API calls.

    Strategy:
    - Single ingredient: TheMealDB first (free, reliable)
    - Multi-ingredient: Spoonacular first (better for complex queries), Tasty as fallback
    - If restrict_ingredients is True: Only search for recipes that use ONLY the specified ingredients
    """
    # Add caching to prevent repeated API calls
    cache_key = _generate_cache_key("intelligent_ingredient_search", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        logger.debug(f"Returning cached intelligent ingredient search results for '{query}'")
        return cached

    # Check if ingredients should be restricted to only those specified
    restrict_ingredients = filters and filters.get('restrict_ingredients', False)

    if restrict_ingredients:
        logger.debug(f"Performing restricted ingredient search for: '{query}'")
        return _restricted_ingredient_search(query, filters)

    results = []

    try:
        # Parse ingredients to determine search strategy
        ingredients = [ing.strip() for ing in query.split(',')]
        is_multi_ingredient = len(ingredients) > 1 or ',' in query

        if not is_multi_ingredient and len(ingredients) == 1 and ingredients[0]:
            # Single ingredient - try TheMealDB first (free and good for single ingredients)
            try:
                themealdb_results = _themealdb_search_by_ingredient(ingredients[0], filters)
                results.extend(themealdb_results)
                logger.debug(f"TheMealDB ingredient search returned {len(themealdb_results)} results")
            except Exception as e:
                logger.error(f"TheMealDB ingredient search failed: {e}")

        # For multi-ingredient searches or if we need more results
        if is_multi_ingredient or len(results) < 10:
            # Strategy: Spoonacular first for multi-ingredient (better complex query handling)
            try:
                spoonacular_results = _spoonacular_search_recipes(query, filters)
                existing_titles = {r.get('title', '').lower() for r in results}
                new_spoonacular = [r for r in spoonacular_results
                                 if r.get('title', '').lower() not in existing_titles]
                results.extend(new_spoonacular)
                logger.debug(f"Spoonacular ingredient search added {len(new_spoonacular)} new results")
            except Exception as e:
                logger.error(f"Spoonacular ingredient search failed: {e}")

        # Fallback to Tasty API if we still need more results (no daily limits)
        if len(results) < 15:
            try:
                tasty_results = _tasty_search_recipes(query, filters)
                existing_titles = {r.get('title', '').lower() for r in results}
                new_tasty = [r for r in tasty_results
                           if r.get('title', '').lower() not in existing_titles]
                results.extend(new_tasty)
                logger.debug(f"Tasty ingredient search added {len(new_tasty)} new results")
            except Exception as e:
                logger.error(f"Tasty ingredient search failed: {e}")

    except Exception as e:
        logger.error(f"Error in intelligent ingredient search: {e}")

    final_results = results[:20]

    # Cache the results for 1 day
    safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
    logger.debug(f"Cached intelligent ingredient search results for '{query}' with {len(final_results)} recipes")

    return final_results

def _intelligent_cuisine_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent cuisine search using multiple APIs strategically.
    Now includes caching to prevent repeated API calls.
    """
    # Add caching to prevent repeated API calls
    cache_key = _generate_cache_key("intelligent_cuisine_search", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        logger.debug(f"Returning cached intelligent cuisine search results for '{query}'")
        return cached

    results = []

    try:
        # TheMealDB is excellent for cuisine searches - use as primary
        themealdb_results = _themealdb_search_by_cuisine(query, filters)
        results.extend(themealdb_results)
        logger.debug(f"TheMealDB cuisine search returned {len(themealdb_results)} results")

        # If we need more results, try Spoonacular
        if len(results) < 15:
            spoonacular_results = _spoonacular_search_by_cuisine(query, filters)
            existing_titles = {r.get('title', '').lower() for r in results}
            new_spoonacular = [r for r in spoonacular_results
                             if r.get('title', '').lower() not in existing_titles]
            results.extend(new_spoonacular)
            logger.debug(f"Spoonacular cuisine search added {len(new_spoonacular)} new results")

    except Exception as e:
        logger.error(f"Error in intelligent cuisine search: {e}")

    final_results = results[:20]

    # Cache the results for 1 day
    safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
    logger.debug(f"Cached intelligent cuisine search results for '{query}' with {len(final_results)} recipes")

    return final_results

# --- Internal helpers for API requests, normalization, and caching will be added here ---

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
