<!-- Modern Quick Filters -->
<div class="quick-filters mb-4 animate-fade-in" x-data="quickFiltersData()">
    <style>
        /* Scaled-down desktop approach for all screen sizes */
        .quick-filters {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-filters .btn-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quick-filters .btn {
            border-radius: 20px;
            padding: 8px 20px;
            border: 1px solid #e9ecef;
            background: white;
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-height: 44px;
        }

        .quick-filters .btn:hover {
            background: #f8f9fa;
            color: #4CAF50;
            border-color: #4CAF50;
        }

        .quick-filters .btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        /* Tablet scaling (768px - 992px) */
        @media (max-width: 992px) and (min-width: 768px) {
            .quick-filters .btn {
                padding: 6px 16px;
                font-size: 13px;
                border-radius: 18px;
                min-height: 40px;
            }

            .quick-filters .btn-group {
                gap: 6px;
            }
        }

        /* Mobile scaling (below 768px) */
        @media (max-width: 767px) {
            .quick-filters .btn {
                padding: 6px 14px;
                font-size: 12px;
                border-radius: 16px;
                min-height: 38px;
            }

            .quick-filters .btn-group {
                gap: 6px;
            }
        }

        /* Small mobile scaling (below 480px) */
        @media (max-width: 479px) {
            .quick-filters .btn {
                padding: 5px 12px;
                font-size: 11px;
                border-radius: 14px;
                min-height: 36px;
            }

            .quick-filters .btn-group {
                gap: 4px;
            }
        }
    </style>

    <div class="btn-group" role="group">
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="quickFilter === 'All' ? 'active' : ''" @click="handleQuickFilter('All')">All</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Trending') ? 'active' : ''"
            @click="handleQuickFilter('Trending')">Trending</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Quick & Easy') ? 'active' : ''"
            @click="handleQuickFilter('Quick & Easy')">Quick & Easy</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Budget') ? 'active' : ''"
            @click="handleQuickFilter('Budget')">Budget</button>
        <button type="button" class="btn btn-secondary-modern focus-ring"
            :class="selectedFilters.includes('Family') ? 'active' : ''"
            @click="handleQuickFilter('Family')">Family</button>
    </div>
</div>

<script>
function quickFiltersData() {
    return {
        quickFilter: 'All',
        selectedFilters: [], // Array to store multiple selected filters
        pendingFilter: null,

        handleQuickFilter(filterType) {
            // Handle 'All' filter - clear all other selections
            if (filterType === 'All') {
                this.quickFilter = 'All';
                this.selectedFilters = [];
            } else {
                // Handle individual filter selection
                this.quickFilter = ''; // Clear 'All' selection

                // Toggle the filter in selectedFilters array
                const index = this.selectedFilters.indexOf(filterType);
                if (index > -1) {
                    // Filter is already selected, remove it
                    this.selectedFilters.splice(index, 1);
                } else {
                    // Filter is not selected, add it
                    this.selectedFilters.push(filterType);
                }

                // If no filters are selected, default back to 'All'
                if (this.selectedFilters.length === 0) {
                    this.quickFilter = 'All';
                }
            }

            // Get the main search form's Alpine data to check for existing results
            const searchForm = document.querySelector('#main-search-box form');
            if (searchForm && searchForm._x_dataStack && searchForm._x_dataStack[0]) {
                const searchData = searchForm._x_dataStack[0];
                searchData.checkForExistingResults();

                // If there are existing results, trigger the confirmation system
                if (searchData.hasExistingResults && !searchData.searchConfirmationNeeded) {
                    searchData.searchConfirmationNeeded = true;

                    // Clear confirmation after 3 seconds
                    if (searchData.confirmationTimeout) clearTimeout(searchData.confirmationTimeout);
                    searchData.confirmationTimeout = setTimeout(() => {
                        searchData.searchConfirmationNeeded = false;
                    }, 3000);

                    // Store the pending filter for second click
                    this.pendingFilter = filterType;
                    return;
                }

                // Second click or no existing results - proceed with filter
                if (searchData.searchConfirmationNeeded && this.pendingFilter === filterType) {
                    searchData.searchConfirmationNeeded = false;
                    if (searchData.confirmationTimeout) clearTimeout(searchData.confirmationTimeout);
                }
            }

            // Apply the filter with haptic feedback on mobile
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            // Trigger search with the selected quick filter(s)
            this.triggerQuickFilterSearch();
        },

        triggerQuickFilterSearch() {
            // Get the main search form to determine current search context
            const mainSearchForm = document.querySelector('#main-search-box form');
            let searchQuery = '';
            let searchType = 'ingredients';
            let hasExistingSearch = false;

            if (mainSearchForm) {
                const queryInput = mainSearchForm.querySelector('[name="search_query"]');
                const typeInput = mainSearchForm.querySelector('[name="search_type"]');
                searchQuery = queryInput ? queryInput.value.trim() : '';
                searchType = typeInput ? typeInput.value : 'ingredients';

                // Check if there are existing search results to determine if we have an active search
                const resultsContainer = document.querySelector('#recipe-results');
                const hasResults = resultsContainer && resultsContainer.children.length > 0;
                hasExistingSearch = searchQuery && hasResults;
            }

            // Determine the filter to send to the backend
            let filterToSend = this.quickFilter;
            if (this.selectedFilters.length > 0) {
                // For multiple filters, send them as a comma-separated string
                filterToSend = this.selectedFilters.join(',');
            }

            // If no existing search, use default search based on active filters
            // This provides a good starting point when no search has been performed
            if (!hasExistingSearch) {
                if (this.selectedFilters.length > 0) {
                    // Use the first selected filter to determine default search
                    const primaryFilter = this.selectedFilters[0];
                    switch(primaryFilter) {
                        case 'Trending':
                            searchQuery = 'popular trending recipes';
                            searchType = 'name';
                            break;
                        case 'Quick & Easy':
                            searchQuery = 'quick easy recipes under 30 minutes';
                            searchType = 'time';
                            break;
                        case 'Budget':
                            searchQuery = 'budget cheap affordable recipes';
                            searchType = 'name';
                            break;
                        case 'Family':
                            searchQuery = 'family friendly recipes';
                            searchType = 'name';
                            break;
                    }
                } else if (this.quickFilter === 'All') {
                    // For 'All', use existing search or default
                    if (!searchQuery) {
                        searchQuery = 'popular recipes';
                        searchType = 'name';
                    }
                }
            }

            // Create a form data object for the search
            const formData = new FormData();
            formData.append('search_type', searchType);
            formData.append('search_query', searchQuery);
            formData.append('quick_filter', filterToSend);

            // Use HTMX to trigger the search
            htmx.ajax('GET', '/recipes/search/?' + new URLSearchParams(formData).toString(), {
                target: '#recipe-results',
                indicator: '.htmx-indicator',
                pushUrl: true
            });
        }
    };
}
</script>
